extends Control

# UI References
@onready var left_arrow = $MainContainer/CarouselContainer/LeftArrow
@onready var right_arrow = $MainContainer/CarouselContainer/RightArrow
@onready var chapter_display = $MainContainer/CarouselContainer/ChapterDisplay
@onready var play_button = $MainContainer/PlayButton
@onready var back_button = $MainContainer/BackButton
@onready var title_label = $MainContainer/TitleLabel

# Chapter display elements
var chapter_title_label: Label
var chapter_description_label: Label
var chapter_preview_image: TextureRect
var chapter_status_label: Label

# Single Chapter Display settings
var current_chapter_index = 0
var total_chapters = 6
var roman_numerals = ["I", "II", "III", "IV", "V", "VI"]

# Chapter data
var chapter_data = [
	{
		"number": 1,
		"title": "BÚRLIVÁ CESTA",
		"description": "Marec 1894. Búrlivá cesta cez karpatské horstvo k Van Helsingovmu zámku. Rozlúštite šifru a nájdite správnu cestu cez temný les k hrôzostrašnému sídlu.",
		"unlocked": true,
		"completed": false
	},
	{
		"number": 2,
		"title": "BRÁNA ZÁMKU",
		"description": "Vstup do zámku cez masívnu železnú bránu zdobenú heraldickými symbolmi. Vyriešte krvavý nápis a prejdite skúškou Rádu, aby ste sa dostali dovnútra.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 3,
		"title": "PÁTRANIE V ZÁMKU",
		"description": "Preskúmanie zámockej knižnice plnej pradávnych kníh a tajomstiev. Objavte obrátené posolstvo a vypočítajte Isabellin rok narodenia.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 4,
		"title": "TAJNÉ KRÍDLO",
		"description": "Preskúmanie tajného krídla zámku s alchymistickým laboratóriom. Absolvujte test pamäte a vyriešte vampírsku aritmetiku.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 5,
		"title": "TEMNÉ KRYPTY",
		"description": "Zostup do pradávnych katakomb pod zámkom. Rozlúštite tieňový kód a nájdite cestu k Isabellinej hrobke cez labyrint kostí.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 6,
		"title": "FINÁLNA KONFRONTÁCIA",
		"description": "Posledná bitka s Isabelle Báthoryovou v jej hrobke. Vyriešte hádanku troch sestier a rituálny rytmus, aby ste ju zastavili navždy.",
		"unlocked": false,
		"completed": false
	}
]

func _ready():
	setup_ui()
	load_chapter_progress()
	create_chapter_display()
	update_chapter_display()
	connect_signals()

func setup_ui():
	# Setup title with enhanced styling
	title_label.text = "◆ KAPITOLY ◆"
	title_label.add_theme_font_size_override("font_size", 36)
	title_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	title_label.add_theme_constant_override("outline_size", 3)
	title_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

	# Setup buttons
	play_button.text = "⚔ HRAŤ KAPITOLU ⚔"
	back_button.text = "← SPÄŤ"

	# Apply RAMCEK styling to buttons
	setup_button_style(play_button)
	setup_button_style(back_button)
	setup_button_style(left_arrow)
	setup_button_style(right_arrow)

	# Setup arrows with better styling
	left_arrow.text = "◀"
	right_arrow.text = "▶"
	left_arrow.add_theme_font_size_override("font_size", 28)
	right_arrow.add_theme_font_size_override("font_size", 28)

	# Single chapter display - no grid needed

func load_chapter_progress():
	# Load from GameManager
	for i in range(total_chapters):
		var chapter_num = i + 1
		chapter_data[i].unlocked = GameManager.is_chapter_unlocked(chapter_num)
		chapter_data[i].completed = chapter_num in GameManager.completed_chapters

func create_chapter_display():
	# Create single chapter display container
	var display_container = VBoxContainer.new()
	display_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	display_container.add_theme_constant_override("margin_left", 40)
	display_container.add_theme_constant_override("margin_top", 20)
	display_container.add_theme_constant_override("margin_right", 40)
	display_container.add_theme_constant_override("margin_bottom", 20)

	# Chapter title with Roman numerals
	chapter_title_label = Label.new()
	chapter_title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	chapter_title_label.add_theme_font_size_override("font_size", 32)
	chapter_title_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	chapter_title_label.add_theme_constant_override("outline_size", 3)
	chapter_title_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))
	display_container.add_child(chapter_title_label)

	# Subtitle with chapter name
	var subtitle_label = Label.new()
	subtitle_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	subtitle_label.add_theme_font_size_override("font_size", 24)
	subtitle_label.add_theme_color_override("font_color", Color(0.95, 0.9, 0.7))
	subtitle_label.add_theme_constant_override("outline_size", 2)
	subtitle_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.6))
	display_container.add_child(subtitle_label)

	# Spacer
	var spacer1 = Control.new()
	spacer1.custom_minimum_size = Vector2(0, 20)
	display_container.add_child(spacer1)

	# Large preview image with frame
	var image_frame = NinePatchRect.new()
	image_frame.texture = load("res://assets/RAMCEK.png")
	image_frame.patch_margin_left = 20
	image_frame.patch_margin_top = 20
	image_frame.patch_margin_right = 20
	image_frame.patch_margin_bottom = 20
	image_frame.custom_minimum_size = Vector2(500, 300)
	image_frame.modulate = Color(0.9, 0.8, 0.6, 0.95)

	chapter_preview_image = TextureRect.new()
	chapter_preview_image.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
	chapter_preview_image.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	chapter_preview_image.offset_left = 20
	chapter_preview_image.offset_top = 20
	chapter_preview_image.offset_right = -20
	chapter_preview_image.offset_bottom = -20
	image_frame.add_child(chapter_preview_image)

	# Center the image frame
	var image_container = HBoxContainer.new()
	image_container.alignment = BoxContainer.ALIGNMENT_CENTER
	image_container.add_child(image_frame)
	display_container.add_child(image_container)

	# Spacer
	var spacer2 = Control.new()
	spacer2.custom_minimum_size = Vector2(0, 20)
	display_container.add_child(spacer2)

	# Chapter description
	chapter_description_label = Label.new()
	chapter_description_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	chapter_description_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	chapter_description_label.add_theme_font_size_override("font_size", 16)
	chapter_description_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6))
	chapter_description_label.add_theme_constant_override("outline_size", 1)
	chapter_description_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.7))
	chapter_description_label.custom_minimum_size = Vector2(600, 80)
	display_container.add_child(chapter_description_label)

	# Spacer
	var spacer3 = Control.new()
	spacer3.custom_minimum_size = Vector2(0, 15)
	display_container.add_child(spacer3)

	# Status badge
	var status_container = HBoxContainer.new()
	status_container.alignment = BoxContainer.ALIGNMENT_CENTER

	var status_badge = NinePatchRect.new()
	status_badge.texture = load("res://assets/RAMCEK.png")
	status_badge.patch_margin_left = 12
	status_badge.patch_margin_top = 12
	status_badge.patch_margin_right = 12
	status_badge.patch_margin_bottom = 12
	status_badge.custom_minimum_size = Vector2(200, 40)

	chapter_status_label = Label.new()
	chapter_status_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	chapter_status_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	chapter_status_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	chapter_status_label.add_theme_font_size_override("font_size", 16)
	chapter_status_label.add_theme_constant_override("outline_size", 1)
	chapter_status_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

	status_badge.add_child(chapter_status_label)
	status_container.add_child(status_badge)
	display_container.add_child(status_container)

	# Store references
	chapter_display.add_child(display_container)
	chapter_display.set_meta("subtitle_label", subtitle_label)
	chapter_display.set_meta("status_badge", status_badge)

func update_chapter_display():
	var chapter = chapter_data[current_chapter_index]
	var subtitle_label = chapter_display.get_meta("subtitle_label")
	var status_badge = chapter_display.get_meta("status_badge")

	# Update title with Roman numerals
	var title_text = "KAPITOLA " + roman_numerals[current_chapter_index]
	chapter_title_label.text = "◆ " + title_text + " ◆"

	# Update subtitle
	subtitle_label.text = chapter.title

	# Update description
	chapter_description_label.text = chapter.description

	# Update preview image
	var preview_path = get_chapter_preview_path(chapter.number)
	if ResourceLoader.exists(preview_path):
		chapter_preview_image.texture = load(preview_path)
	else:
		# Create fallback
		chapter_preview_image.texture = null

	# Update status
	if chapter.completed:
		chapter_status_label.text = "✓ DOKONČENÉ"
		chapter_status_label.add_theme_color_override("font_color", Color(0.2, 0.8, 0.2))
		status_badge.modulate = Color(0.2, 0.8, 0.2, 0.3)
	elif chapter.unlocked:
		chapter_status_label.text = "⚡ DOSTUPNÉ"
		chapter_status_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2))
		status_badge.modulate = Color(1.0, 0.8, 0.2, 0.3)
	else:
		chapter_status_label.text = "🔒 UZAMKNUTÉ"
		chapter_status_label.add_theme_color_override("font_color", Color(0.6, 0.6, 0.6))
		status_badge.modulate = Color(0.4, 0.4, 0.4, 0.3)

	# Update navigation arrows
	update_navigation_arrows()

	# Update play button
	var current_chapter = chapter_data[current_chapter_index]
	if current_chapter.unlocked:
		play_button.disabled = false
		play_button.modulate = Color.WHITE
		play_button.text = "⚔ HRAŤ KAPITOLU " + roman_numerals[current_chapter_index] + " ⚔"
	else:
		play_button.disabled = true
		play_button.modulate = Color(0.5, 0.5, 0.5)
		play_button.text = "🔒 KAPITOLA UZAMKNUTÁ"

func update_navigation_arrows():
	# Update arrow states and text
	left_arrow.text = "◀ PREDCHÁDZAJÚCA"
	right_arrow.text = "NASLEDUJÚCA ▶"

	# Always enable arrows for cycling
	left_arrow.disabled = false
	right_arrow.disabled = false
	left_arrow.modulate = Color(0.831, 0.686, 0.216)
	right_arrow.modulate = Color(0.831, 0.686, 0.216)

func navigate(direction: int):
	# Cycle through chapters
	current_chapter_index += direction

	if current_chapter_index < 0:
		current_chapter_index = total_chapters - 1
	elif current_chapter_index >= total_chapters:
		current_chapter_index = 0

	# Play navigation sound
	AudioManager.play_menu_button_sound()

	# Animate transition
	animate_chapter_transition(direction)

func animate_chapter_transition(direction: int):
	var tween = create_tween()
	tween.set_parallel(true)

	# Fade out current display
	tween.tween_property(chapter_display, "modulate:a", 0.0, 0.3)
	if direction > 0:
		tween.tween_property(chapter_display, "position:x", -100, 0.3)
	else:
		tween.tween_property(chapter_display, "position:x", 100, 0.3)

	await tween.finished

	# Update content
	update_chapter_display()

	# Reset position for fade in
	if direction > 0:
		chapter_display.position.x = 100
	else:
		chapter_display.position.x = -100

	# Fade in new display
	var tween_in = create_tween()
	tween_in.set_parallel(true)
	tween_in.tween_property(chapter_display, "position:x", 0, 0.3)
	tween_in.tween_property(chapter_display, "modulate:a", 1.0, 0.3)

# Removed old carousel functions - now using single chapter display

# Old functions removed: create_chapter_card, update_carousel_display, etc.
# Now using single chapter display with navigation

func get_chapter_preview_path(chapter_number: int) -> String:
	var card = Control.new()
	card.custom_minimum_size = Vector2(220, 380)

	# Shadow effect
	var shadow = NinePatchRect.new()
	shadow.texture = load("res://assets/RAMCEK.png")
	shadow.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	shadow.offset_left = 4
	shadow.offset_top = 4
	shadow.offset_right = 4
	shadow.offset_bottom = 4
	shadow.patch_margin_left = 16
	shadow.patch_margin_top = 16
	shadow.patch_margin_right = 16
	shadow.patch_margin_bottom = 16
	shadow.modulate = Color(0, 0, 0, 0.3)
	card.add_child(shadow)

	# Background frame
	var bg = NinePatchRect.new()
	bg.texture = load("res://assets/RAMCEK.png")
	bg.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	bg.patch_margin_left = 16
	bg.patch_margin_top = 16
	bg.patch_margin_right = 16
	bg.patch_margin_bottom = 16
	card.add_child(bg)

	# Inner glow effect
	var glow = NinePatchRect.new()
	glow.texture = load("res://assets/RAMCEK.png")
	glow.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	glow.offset_left = 2
	glow.offset_top = 2
	glow.offset_right = -2
	glow.offset_bottom = -2
	glow.patch_margin_left = 16
	glow.patch_margin_top = 16
	glow.patch_margin_right = 16
	glow.patch_margin_bottom = 16
	glow.modulate = Color(1, 1, 1, 0.1)
	card.add_child(glow)

	# Container for content
	var container = VBoxContainer.new()
	container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	container.add_theme_constant_override("margin_left", 20)
	container.add_theme_constant_override("margin_top", 20)
	container.add_theme_constant_override("margin_right", 20)
	container.add_theme_constant_override("margin_bottom", 20)
	card.add_child(container)

	# Chapter number with decorative elements
	var number_container = HBoxContainer.new()
	number_container.alignment = BoxContainer.ALIGNMENT_CENTER

	var left_ornament = Label.new()
	left_ornament.text = "◆"
	left_ornament.add_theme_font_size_override("font_size", 12)
	left_ornament.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))

	var number_label = Label.new()
	number_label.text = " KAPITOLA " + str(data.number) + " "
	number_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	number_label.add_theme_font_size_override("font_size", 18)
	number_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	# Add outline for better readability
	number_label.add_theme_constant_override("outline_size", 2)
	number_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

	var right_ornament = Label.new()
	right_ornament.text = "◆"
	right_ornament.add_theme_font_size_override("font_size", 12)
	right_ornament.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))

	number_container.add_child(left_ornament)
	number_container.add_child(number_label)
	number_container.add_child(right_ornament)
	container.add_child(number_container)

	# Chapter title with better styling
	var title_label = Label.new()
	title_label.text = data.title
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	title_label.add_theme_font_size_override("font_size", 16)
	title_label.add_theme_color_override("font_color", Color(0.95, 0.9, 0.7))
	title_label.add_theme_constant_override("outline_size", 1)
	title_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.6))
	container.add_child(title_label)

	# Decorative separator
	var separator_container = HBoxContainer.new()
	separator_container.alignment = BoxContainer.ALIGNMENT_CENTER
	var separator = Label.new()
	separator.text = "━━━━━━━━━━━━━━━━━━━━"
	separator.add_theme_font_size_override("font_size", 8)
	separator.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216, 0.6))
	separator_container.add_child(separator)
	container.add_child(separator_container)

	# Small spacer before image
	var spacer1 = Control.new()
	spacer1.custom_minimum_size = Vector2(0, 8)
	container.add_child(spacer1)

	# Chapter preview image with decorative frame
	var preview_frame = NinePatchRect.new()
	preview_frame.texture = load("res://assets/RAMCEK.png")
	preview_frame.patch_margin_left = 12
	preview_frame.patch_margin_top = 12
	preview_frame.patch_margin_right = 12
	preview_frame.patch_margin_bottom = 12
	preview_frame.custom_minimum_size = Vector2(180, 100)
	preview_frame.modulate = Color(0.9, 0.8, 0.6, 0.95)

	var preview_path = get_chapter_preview_path(data.number)
	if ResourceLoader.exists(preview_path):
		var preview = TextureRect.new()
		preview.texture = load(preview_path)
		preview.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
		preview.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		preview.offset_left = 12
		preview.offset_top = 12
		preview.offset_right = -12
		preview.offset_bottom = -12
		preview_frame.add_child(preview)

		# Add decorative border overlay
		var border_overlay = NinePatchRect.new()
		border_overlay.texture = load("res://assets/RAMCEK.png")
		border_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		border_overlay.offset_left = 8
		border_overlay.offset_top = 8
		border_overlay.offset_right = -8
		border_overlay.offset_bottom = -8
		border_overlay.patch_margin_left = 8
		border_overlay.patch_margin_top = 8
		border_overlay.patch_margin_right = 8
		border_overlay.patch_margin_bottom = 8
		border_overlay.modulate = Color(0.831, 0.686, 0.216, 0.3)
		preview_frame.add_child(border_overlay)
	else:
		# Fallback with gothic styling
		var fallback = ColorRect.new()
		fallback.color = Color(0.1, 0.1, 0.15, 0.9)
		fallback.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		fallback.offset_left = 12
		fallback.offset_top = 12
		fallback.offset_right = -12
		fallback.offset_bottom = -12
		preview_frame.add_child(fallback)

		# Add placeholder with gothic styling
		var placeholder = Label.new()
		placeholder.text = "◆ " + str(data.number) + " ◆"
		placeholder.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		placeholder.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
		placeholder.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		placeholder.add_theme_font_size_override("font_size", 16)
		placeholder.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
		placeholder.add_theme_constant_override("outline_size", 1)
		placeholder.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))
		fallback.add_child(placeholder)

	container.add_child(preview_frame)

	# Small spacer after image
	var spacer2 = Control.new()
	spacer2.custom_minimum_size = Vector2(0, 8)
	container.add_child(spacer2)

	# Chapter description
	var description = get_chapter_description(data.number)
	if description != "":
		var desc_label = Label.new()
		desc_label.text = description
		desc_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		desc_label.vertical_alignment = VERTICAL_ALIGNMENT_TOP
		desc_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
		desc_label.add_theme_font_size_override("font_size", 11)
		desc_label.add_theme_color_override("font_color", Color(0.85, 0.75, 0.55))
		desc_label.add_theme_constant_override("outline_size", 1)
		desc_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.7))
		desc_label.custom_minimum_size = Vector2(180, 60)
		container.add_child(desc_label)

	# Small spacer before status
	var spacer3 = Control.new()
	spacer3.custom_minimum_size = Vector2(0, 5)
	container.add_child(spacer3)

	# Progress/Lock status with better design
	var status_container = VBoxContainer.new()
	status_container.alignment = BoxContainer.ALIGNMENT_CENTER

	# Status badge
	var status_badge = NinePatchRect.new()
	status_badge.texture = load("res://assets/RAMCEK.png")
	status_badge.patch_margin_left = 6
	status_badge.patch_margin_top = 6
	status_badge.patch_margin_right = 6
	status_badge.patch_margin_bottom = 6
	status_badge.custom_minimum_size = Vector2(140, 32)

	var status_label = Label.new()
	status_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	status_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	status_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	status_label.add_theme_font_size_override("font_size", 12)
	status_label.add_theme_constant_override("outline_size", 1)
	status_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

	if data.completed:
		status_label.text = "✓ DOKONČENÉ"
		status_label.add_theme_color_override("font_color", Color(0.2, 0.8, 0.2))
		status_badge.modulate = Color(0.2, 0.8, 0.2, 0.3)
	elif data.unlocked:
		status_label.text = "⚡ DOSTUPNÉ"
		status_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2))
		status_badge.modulate = Color(1.0, 0.8, 0.2, 0.3)
	else:
		status_label.text = "🔒 UZAMKNUTÉ"
		status_label.add_theme_color_override("font_color", Color(0.6, 0.6, 0.6))
		status_badge.modulate = Color(0.4, 0.4, 0.4, 0.3)

	status_badge.add_child(status_label)
	status_container.add_child(status_badge)
	container.add_child(status_container)

	# Make card clickable
	var button = Button.new()
	button.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	button.flat = true
	button.pressed.connect(func(): select_chapter(data.number))

	# Add hover effects
	button.mouse_entered.connect(func(): _on_card_mouse_entered(card))
	button.mouse_exited.connect(func(): _on_card_mouse_exited(card))

	# Add tooltip
	if data.unlocked:
		if data.completed:
			button.tooltip_text = "Kapitola dokončená! Kliknite pre opätovné hranie."
		else:
			button.tooltip_text = "Kliknite pre výber tejto kapitoly."
	else:
		button.tooltip_text = "Kapitola je uzamknutá. Dokončite predchádzajúce kapitoly."

	card.add_child(button)

	# Visual feedback for selection
	if data.number == selected_chapter:
		bg.modulate = Color(1.3, 1.2, 0.8)  # Golden highlight
		shadow.modulate = Color(0.8, 0.6, 0.2, 0.5)  # Golden shadow
		glow.modulate = Color(1, 1, 0.5, 0.3)  # Golden glow
	else:
		bg.modulate = Color.WHITE
		shadow.modulate = Color(0, 0, 0, 0.3)
		glow.modulate = Color(1, 1, 1, 0.1)

	# Store references for animations
	card.set_meta("bg", bg)
	card.set_meta("shadow", shadow)
	card.set_meta("glow", glow)
	card.set_meta("data", data)

	return card

# === OLD FUNCTIONS REMOVED ===
# The following functions were part of the old carousel system and are no longer needed:
# - update_carousel_display()
# - animate_carousel_transition()
# - select_chapter()
# - refresh_chapter_cards()
# - create_chapter_card() (partially removed above)

func get_chapter_preview_path(chapter_number: int) -> String:
	# Použiť prvý obrázok z každej kapitoly ako preview
	match chapter_number:
		1:
			return "res://assets/Obrázky/Kapitola_1/1.png"
		2:
			return "res://assets/pozadia/Kapitola_2/1.png"
		3:
			return "res://assets/pozadia/Kapitola_3/1.png"
		4:
			return "res://assets/pozadia/Kapitola_4/1.png"
		5:
			return "res://assets/pozadia/Kapitola_5/1.png"
		6:
			return "res://assets/pozadia/Kapitola_6/1.png"
		_:
			return ""

func connect_signals():
	left_arrow.pressed.connect(_on_left_arrow_pressed)
	right_arrow.pressed.connect(_on_right_arrow_pressed)
	play_button.pressed.connect(_on_play_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)

func _on_left_arrow_pressed():
	navigate(-1)

func _on_right_arrow_pressed():
	navigate(1)

func _on_play_button_pressed():
	var current_chapter = chapter_data[current_chapter_index]
	if not current_chapter.unlocked:
		AudioManager.play_puzzle_error_sound()
		show_error_message("Táto kapitola je uzamknutá!")
		return

	AudioManager.play_menu_button_sound()
	show_loading_transition()
	await get_tree().create_timer(0.5).timeout
	GameManager.go_to_chapter(current_chapter.number)

func show_error_message(message: String):
	# Create temporary error message
	var error_label = Label.new()
	error_label.text = message
	error_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	error_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	error_label.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	error_label.offset_left = -150
	error_label.offset_right = 150
	error_label.offset_top = -25
	error_label.offset_bottom = 25
	error_label.add_theme_font_size_override("font_size", 18)
	error_label.add_theme_color_override("font_color", Color.RED)
	error_label.add_theme_constant_override("outline_size", 2)
	error_label.add_theme_color_override("font_outline_color", Color.BLACK)

	# Add background
	var error_bg = NinePatchRect.new()
	error_bg.texture = load("res://assets/RAMCEK.png")
	error_bg.patch_margin_left = 16
	error_bg.patch_margin_top = 16
	error_bg.patch_margin_right = 16
	error_bg.patch_margin_bottom = 16
	error_bg.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	error_bg.modulate = Color(0.8, 0.2, 0.2, 0.8)
	error_label.add_child(error_bg)
	error_label.move_child(error_bg, 0)

	add_child(error_label)

	# Animate and remove
	var tween = create_tween()
	tween.tween_property(error_label, "modulate:a", 0.0, 2.0)
	await tween.finished
	error_label.queue_free()

func show_loading_transition():
	# Create loading overlay
	var loading_overlay = ColorRect.new()
	loading_overlay.color = Color(0, 0, 0, 0.8)
	loading_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)

	var loading_label = Label.new()
	loading_label.text = "Načítavam kapitolu..."
	loading_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	loading_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	loading_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	loading_label.add_theme_font_size_override("font_size", 24)
	loading_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	loading_label.add_theme_constant_override("outline_size", 2)
	loading_label.add_theme_color_override("font_outline_color", Color.BLACK)

	loading_overlay.add_child(loading_label)
	add_child(loading_overlay)

	# Fade in
	loading_overlay.modulate.a = 0.0
	var tween = create_tween()
	tween.tween_property(loading_overlay, "modulate:a", 1.0, 0.3)

func _on_back_button_pressed():
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_button_pressed()

# Old card hover functions removed - not needed for single chapter display

func get_chapter_preview_path(chapter_number: int) -> String:
	# Použiť prvý obrázok z každej kapitoly ako preview
	match chapter_number:
		1:
			return "res://assets/Obrázky/Kapitola_1/1.png"
		2:
			return "res://assets/pozadia/Kapitola_2/1.png"
		3:
			return "res://assets/pozadia/Kapitola_3/1.png"
		4:
			return "res://assets/pozadia/Kapitola_4/1.png"
		5:
			return "res://assets/pozadia/Kapitola_5/1.png"
		6:
			return "res://assets/pozadia/Kapitola_6/1.png"
		_:
			return ""

func get_chapter_description(chapter_number: int) -> String:
	# Krátke popisy kapitol
	match chapter_number:
		1:
			return "Búrlivá cesta cez karpatské horstvo k Van Helsingovmu zámku. Rozlúštite šifru a nájdite správnu cestu."
		2:
			return "Vstup do zámku cez masívnu bránu. Vyriešte krvavý nápis a prejdite skúškou Rádu."
		3:
			return "Pátranie v zámockej knižnici. Objavte obrátené posolstvo a vypočítajte Isabellin rok narodenia."
		4:
			return "Preskúmanie tajného krídla. Test pamäte a vampírska aritmetika v alchymistickom laboratóriu."
		5:
			return "Zostup do pradávnych katakomb. Rozlúštite tieňový kód a nájdite cestu k hrobke."
		6:
			return "Finálna konfrontácia s Isabelle. Vyriešte hádanku troch sestier a rituálny rytmus."
		_:
			return ""

func setup_button_style(button: Button):
	# Apply RAMCEK frame to button
	var ramcek_texture = load("res://assets/RAMCEK.png")

	# Create StyleBoxTexture for normal state
	var style_normal = StyleBoxTexture.new()
	style_normal.texture = ramcek_texture
	style_normal.texture_margin_left = 16
	style_normal.texture_margin_top = 16
	style_normal.texture_margin_right = 16
	style_normal.texture_margin_bottom = 16

	# Create StyleBoxTexture for hover state
	var style_hover = StyleBoxTexture.new()
	style_hover.texture = ramcek_texture
	style_hover.texture_margin_left = 16
	style_hover.texture_margin_top = 16
	style_hover.texture_margin_right = 16
	style_hover.texture_margin_bottom = 16
	style_hover.modulate_color = Color(1.2, 1.2, 1.0)  # Golden tint

	# Apply styles
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)
	button.add_theme_stylebox_override("pressed", style_hover)

	# Center text
	button.alignment = HORIZONTAL_ALIGNMENT_CENTER

# Old page indicator and progress indicator functions removed - not needed for single chapter display
