extends Control

# UI References
@onready var left_arrow = $MainContainer/CarouselContainer/LeftArrow
@onready var right_arrow = $MainContainer/CarouselContainer/RightArrow
@onready var chapter_grid = $MainContainer/CarouselContainer/ChapterGrid
@onready var play_button = $MainContainer/PlayButton
@onready var back_button = $MainContainer/BackButton
@onready var title_label = $MainContainer/TitleLabel

# Carousel settings
var current_page = 0
var chapters_per_page = 3
var total_chapters = 6
var total_pages = 2
var selected_chapter = 1

# Chapter data
var chapter_data = [
	{"number": 1, "title": "BÚRLIVÁ CESTA", "unlocked": true, "completed": false},
	{"number": 2, "title": "BRÁNA ZÁMKU", "unlocked": false, "completed": false},
	{"number": 3, "title": "PÁTRANIE V ZÁMKU", "unlocked": false, "completed": false},
	{"number": 4, "title": "TAJNÉ KRÍDLO", "unlocked": false, "completed": false},
	{"number": 5, "title": "TEMNÉ KRYPTY", "unlocked": false, "completed": false},
	{"number": 6, "title": "FINÁLNA BITKA", "unlocked": false, "completed": false}
]

func _ready():
	setup_ui()
	load_chapter_progress()
	setup_chapter_cards()
	update_carousel_display()
	connect_signals()

func setup_ui():
	# Setup title with enhanced styling
	title_label.text = "◆ KAPITOLY ◆"
	title_label.add_theme_font_size_override("font_size", 36)
	title_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	title_label.add_theme_constant_override("outline_size", 3)
	title_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

	# Setup buttons
	play_button.text = "⚔ HRAŤ KAPITOLU ⚔"
	back_button.text = "← SPÄŤ"

	# Apply RAMCEK styling to buttons
	setup_button_style(play_button)
	setup_button_style(back_button)
	setup_button_style(left_arrow)
	setup_button_style(right_arrow)

	# Setup arrows with better styling
	left_arrow.text = "◀"
	right_arrow.text = "▶"
	left_arrow.add_theme_font_size_override("font_size", 28)
	right_arrow.add_theme_font_size_override("font_size", 28)

	# Setup grid
	chapter_grid.columns = chapters_per_page
	chapter_grid.add_theme_constant_override("h_separation", 30)

	# Add page indicator
	create_page_indicator()

	# Add progress indicator
	create_progress_indicator()

func load_chapter_progress():
	# Load from GameManager
	for i in range(total_chapters):
		var chapter_num = i + 1
		chapter_data[i].unlocked = GameManager.is_chapter_unlocked(chapter_num)
		chapter_data[i].completed = chapter_num in GameManager.completed_chapters

func setup_chapter_cards():
	# Create chapter cards
	for i in range(total_chapters):
		var card = create_chapter_card(chapter_data[i])
		chapter_grid.add_child(card)

func create_chapter_card(data: Dictionary) -> Control:
	var card = Control.new()
	card.custom_minimum_size = Vector2(240, 320)

	# Shadow effect
	var shadow = NinePatchRect.new()
	shadow.texture = load("res://assets/RAMCEK.png")
	shadow.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	shadow.offset_left = 4
	shadow.offset_top = 4
	shadow.offset_right = 4
	shadow.offset_bottom = 4
	shadow.patch_margin_left = 16
	shadow.patch_margin_top = 16
	shadow.patch_margin_right = 16
	shadow.patch_margin_bottom = 16
	shadow.modulate = Color(0, 0, 0, 0.3)
	card.add_child(shadow)

	# Background frame
	var bg = NinePatchRect.new()
	bg.texture = load("res://assets/RAMCEK.png")
	bg.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	bg.patch_margin_left = 16
	bg.patch_margin_top = 16
	bg.patch_margin_right = 16
	bg.patch_margin_bottom = 16
	card.add_child(bg)

	# Inner glow effect
	var glow = NinePatchRect.new()
	glow.texture = load("res://assets/RAMCEK.png")
	glow.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	glow.offset_left = 2
	glow.offset_top = 2
	glow.offset_right = -2
	glow.offset_bottom = -2
	glow.patch_margin_left = 16
	glow.patch_margin_top = 16
	glow.patch_margin_right = 16
	glow.patch_margin_bottom = 16
	glow.modulate = Color(1, 1, 1, 0.1)
	card.add_child(glow)

	# Container for content
	var container = VBoxContainer.new()
	container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	container.add_theme_constant_override("margin_left", 20)
	container.add_theme_constant_override("margin_top", 20)
	container.add_theme_constant_override("margin_right", 20)
	container.add_theme_constant_override("margin_bottom", 20)
	card.add_child(container)

	# Chapter number with decorative elements
	var number_container = HBoxContainer.new()
	number_container.alignment = BoxContainer.ALIGNMENT_CENTER

	var left_ornament = Label.new()
	left_ornament.text = "◆"
	left_ornament.add_theme_font_size_override("font_size", 12)
	left_ornament.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))

	var number_label = Label.new()
	number_label.text = " KAPITOLA " + str(data.number) + " "
	number_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	number_label.add_theme_font_size_override("font_size", 18)
	number_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	# Add outline for better readability
	number_label.add_theme_constant_override("outline_size", 2)
	number_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

	var right_ornament = Label.new()
	right_ornament.text = "◆"
	right_ornament.add_theme_font_size_override("font_size", 12)
	right_ornament.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))

	number_container.add_child(left_ornament)
	number_container.add_child(number_label)
	number_container.add_child(right_ornament)
	container.add_child(number_container)

	# Chapter title with better styling
	var title_label = Label.new()
	title_label.text = data.title
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	title_label.add_theme_font_size_override("font_size", 16)
	title_label.add_theme_color_override("font_color", Color(0.95, 0.9, 0.7))
	title_label.add_theme_constant_override("outline_size", 1)
	title_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.6))
	container.add_child(title_label)

	# Decorative separator
	var separator_container = HBoxContainer.new()
	separator_container.alignment = BoxContainer.ALIGNMENT_CENTER
	var separator = Label.new()
	separator.text = "━━━━━━━━━━━━━━━━━━━━"
	separator.add_theme_font_size_override("font_size", 8)
	separator.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216, 0.6))
	separator_container.add_child(separator)
	container.add_child(separator_container)

	# Spacer
	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 5)
	container.add_child(spacer)

	# Chapter preview image with frame
	var preview_frame = NinePatchRect.new()
	preview_frame.texture = load("res://assets/RAMCEK.png")
	preview_frame.patch_margin_left = 8
	preview_frame.patch_margin_top = 8
	preview_frame.patch_margin_right = 8
	preview_frame.patch_margin_bottom = 8
	preview_frame.custom_minimum_size = Vector2(200, 130)
	preview_frame.modulate = Color(0.8, 0.8, 0.8, 0.9)

	var preview_path = get_chapter_preview_path(data.number)
	if ResourceLoader.exists(preview_path):
		var preview = TextureRect.new()
		preview.texture = load(preview_path)
		preview.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
		preview.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		preview.offset_left = 8
		preview.offset_top = 8
		preview.offset_right = -8
		preview.offset_bottom = -8
		preview_frame.add_child(preview)

		# Add subtle overlay for better text readability
		var overlay = ColorRect.new()
		overlay.color = Color(0, 0, 0, 0.2)
		overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		preview.add_child(overlay)
	else:
		# Fallback with gradient
		var fallback = ColorRect.new()
		fallback.color = Color(0.15, 0.15, 0.25, 0.9)
		fallback.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		fallback.offset_left = 8
		fallback.offset_top = 8
		fallback.offset_right = -8
		fallback.offset_bottom = -8
		preview_frame.add_child(fallback)

		# Add placeholder text
		var placeholder = Label.new()
		placeholder.text = "Kapitola " + str(data.number)
		placeholder.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		placeholder.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
		placeholder.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
		placeholder.add_theme_font_size_override("font_size", 14)
		placeholder.add_theme_color_override("font_color", Color(0.7, 0.7, 0.7))
		fallback.add_child(placeholder)

	container.add_child(preview_frame)

	# Progress/Lock status with better design
	var status_container = VBoxContainer.new()
	status_container.alignment = BoxContainer.ALIGNMENT_CENTER

	# Status badge
	var status_badge = NinePatchRect.new()
	status_badge.texture = load("res://assets/RAMCEK.png")
	status_badge.patch_margin_left = 8
	status_badge.patch_margin_top = 8
	status_badge.patch_margin_right = 8
	status_badge.patch_margin_bottom = 8
	status_badge.custom_minimum_size = Vector2(160, 40)

	var status_label = Label.new()
	status_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	status_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	status_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	status_label.add_theme_font_size_override("font_size", 14)
	status_label.add_theme_constant_override("outline_size", 1)
	status_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

	if data.completed:
		status_label.text = "✓ DOKONČENÉ"
		status_label.add_theme_color_override("font_color", Color(0.2, 0.8, 0.2))
		status_badge.modulate = Color(0.2, 0.8, 0.2, 0.3)
	elif data.unlocked:
		status_label.text = "⚡ DOSTUPNÉ"
		status_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2))
		status_badge.modulate = Color(1.0, 0.8, 0.2, 0.3)
	else:
		status_label.text = "🔒 UZAMKNUTÉ"
		status_label.add_theme_color_override("font_color", Color(0.6, 0.6, 0.6))
		status_badge.modulate = Color(0.4, 0.4, 0.4, 0.3)

	status_badge.add_child(status_label)
	status_container.add_child(status_badge)
	container.add_child(status_container)

	# Make card clickable
	var button = Button.new()
	button.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	button.flat = true
	button.pressed.connect(func(): select_chapter(data.number))

	# Add hover effects
	button.mouse_entered.connect(func(): _on_card_mouse_entered(card))
	button.mouse_exited.connect(func(): _on_card_mouse_exited(card))

	# Add tooltip
	if data.unlocked:
		if data.completed:
			button.tooltip_text = "Kapitola dokončená! Kliknite pre opätovné hranie."
		else:
			button.tooltip_text = "Kliknite pre výber tejto kapitoly."
	else:
		button.tooltip_text = "Kapitola je uzamknutá. Dokončite predchádzajúce kapitoly."

	card.add_child(button)

	# Visual feedback for selection
	if data.number == selected_chapter:
		bg.modulate = Color(1.3, 1.2, 0.8)  # Golden highlight
		shadow.modulate = Color(0.8, 0.6, 0.2, 0.5)  # Golden shadow
		glow.modulate = Color(1, 1, 0.5, 0.3)  # Golden glow
	else:
		bg.modulate = Color.WHITE
		shadow.modulate = Color(0, 0, 0, 0.3)
		glow.modulate = Color(1, 1, 1, 0.1)

	# Store references for animations
	card.set_meta("bg", bg)
	card.set_meta("shadow", shadow)
	card.set_meta("glow", glow)
	card.set_meta("data", data)

	return card

func update_carousel_display():
	# Hide all cards first
	for card in chapter_grid.get_children():
		card.visible = false

	# Show cards for current page
	var start_index = current_page * chapters_per_page
	var end_index = min(start_index + chapters_per_page, total_chapters)

	for i in range(start_index, end_index):
		if i < chapter_grid.get_child_count():
			chapter_grid.get_child(i).visible = true

	# Update arrow visibility and styling
	left_arrow.disabled = (current_page == 0)
	right_arrow.disabled = (current_page >= total_pages - 1)

	# Enhanced arrow visual state
	if left_arrow.disabled:
		left_arrow.modulate = Color(0.4, 0.4, 0.4, 0.6)
		left_arrow.text = "◁"
	else:
		left_arrow.modulate = Color(0.831, 0.686, 0.216)
		left_arrow.text = "◀"

	if right_arrow.disabled:
		right_arrow.modulate = Color(0.4, 0.4, 0.4, 0.6)
		right_arrow.text = "▷"
	else:
		right_arrow.modulate = Color(0.831, 0.686, 0.216)
		right_arrow.text = "▶"

	# Update page indicator
	update_page_indicator()

func connect_signals():
	left_arrow.pressed.connect(_on_left_arrow_pressed)
	right_arrow.pressed.connect(_on_right_arrow_pressed)
	play_button.pressed.connect(_on_play_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)

func _on_left_arrow_pressed():
	if current_page > 0:
		current_page -= 1
		AudioManager.play_menu_button_sound()
		animate_carousel_transition("left")

func _on_right_arrow_pressed():
	if current_page < total_pages - 1:
		current_page += 1
		AudioManager.play_menu_button_sound()
		animate_carousel_transition("right")

func animate_carousel_transition(direction: String):
	var tween = create_tween()
	tween.set_parallel(true)

	# Fade out and slide current page
	tween.tween_property(chapter_grid, "modulate:a", 0.0, 0.2)
	if direction == "left":
		tween.tween_property(chapter_grid, "position:x", 150, 0.4)
	else:
		tween.tween_property(chapter_grid, "position:x", -150, 0.4)

	# Scale down slightly during transition
	tween.tween_property(chapter_grid, "scale", Vector2(0.9, 0.9), 0.2)

	# Wait and update display
	await tween.finished
	update_carousel_display()

	# Reset position for slide in
	if direction == "left":
		chapter_grid.position.x = -150
	else:
		chapter_grid.position.x = 150

	# Slide in and fade in new page
	var tween_in = create_tween()
	tween_in.set_parallel(true)
	tween_in.tween_property(chapter_grid, "position:x", 0, 0.4)
	tween_in.tween_property(chapter_grid, "modulate:a", 1.0, 0.3)
	tween_in.tween_property(chapter_grid, "scale", Vector2(1.0, 1.0), 0.3)

func select_chapter(chapter_number: int):
	if not chapter_data[chapter_number - 1].unlocked:
		AudioManager.play_puzzle_error_sound()
		# Find the card and show lock animation
		var card_index = chapter_number - 1
		if card_index < chapter_grid.get_child_count():
			var card = chapter_grid.get_child(card_index)
			show_locked_animation(card)
		return

	selected_chapter = chapter_number
	AudioManager.play_menu_button_sound()

	# Update visual selection
	refresh_chapter_cards()

func refresh_chapter_cards():
	# Remove old cards and recreate
	for card in chapter_grid.get_children():
		card.queue_free()

	await get_tree().process_frame
	setup_chapter_cards()
	update_carousel_display()

func _on_play_button_pressed():
	if not chapter_data[selected_chapter - 1].unlocked:
		AudioManager.play_puzzle_error_sound()
		show_error_message("Táto kapitola je uzamknutá!")
		return

	AudioManager.play_menu_button_sound()
	show_loading_transition()
	await get_tree().create_timer(0.5).timeout
	GameManager.go_to_chapter(selected_chapter)

func show_error_message(message: String):
	# Create temporary error message
	var error_label = Label.new()
	error_label.text = message
	error_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	error_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	error_label.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	error_label.offset_left = -150
	error_label.offset_right = 150
	error_label.offset_top = -25
	error_label.offset_bottom = 25
	error_label.add_theme_font_size_override("font_size", 18)
	error_label.add_theme_color_override("font_color", Color.RED)
	error_label.add_theme_constant_override("outline_size", 2)
	error_label.add_theme_color_override("font_outline_color", Color.BLACK)

	# Add background
	var error_bg = NinePatchRect.new()
	error_bg.texture = load("res://assets/RAMCEK.png")
	error_bg.patch_margin_left = 16
	error_bg.patch_margin_top = 16
	error_bg.patch_margin_right = 16
	error_bg.patch_margin_bottom = 16
	error_bg.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	error_bg.modulate = Color(0.8, 0.2, 0.2, 0.8)
	error_label.add_child(error_bg)
	error_label.move_child(error_bg, 0)

	add_child(error_label)

	# Animate and remove
	var tween = create_tween()
	tween.tween_property(error_label, "modulate:a", 0.0, 2.0)
	await tween.finished
	error_label.queue_free()

func show_loading_transition():
	# Create loading overlay
	var loading_overlay = ColorRect.new()
	loading_overlay.color = Color(0, 0, 0, 0.8)
	loading_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)

	var loading_label = Label.new()
	loading_label.text = "Načítavam kapitolu..."
	loading_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	loading_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	loading_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	loading_label.add_theme_font_size_override("font_size", 24)
	loading_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	loading_label.add_theme_constant_override("outline_size", 2)
	loading_label.add_theme_color_override("font_outline_color", Color.BLACK)

	loading_overlay.add_child(loading_label)
	add_child(loading_overlay)

	# Fade in
	loading_overlay.modulate.a = 0.0
	var tween = create_tween()
	tween.tween_property(loading_overlay, "modulate:a", 1.0, 0.3)

func _on_back_button_pressed():
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_button_pressed()

# Enhanced hover effects pre cards
func _on_card_mouse_entered(card: Control):
	var data = card.get_meta("data")
	var bg = card.get_meta("bg")
	var shadow = card.get_meta("shadow")
	var glow = card.get_meta("glow")

	if data.unlocked:
		var tween = create_tween()
		tween.set_parallel(true)

		# Scale and lift effect
		tween.tween_property(card, "scale", Vector2(1.08, 1.08), 0.3)
		tween.tween_property(card, "position:y", card.position.y - 5, 0.3)

		# Enhanced glow and shadow
		if data.number != selected_chapter:
			tween.tween_property(bg, "modulate", Color(1.1, 1.1, 0.9), 0.3)
		tween.tween_property(shadow, "modulate", Color(0, 0, 0, 0.6), 0.3)
		tween.tween_property(glow, "modulate", Color(1, 1, 0.8, 0.4), 0.3)

		AudioManager.play_ui_sound("res://audio/UI_SOUNDS/menu_sound.wav")
	else:
		# Subtle shake for locked chapters
		show_locked_animation(card)

func _on_card_mouse_exited(card: Control):
	var data = card.get_meta("data")
	var bg = card.get_meta("bg")
	var shadow = card.get_meta("shadow")
	var glow = card.get_meta("glow")

	var tween = create_tween()
	tween.set_parallel(true)

	# Return to normal
	tween.tween_property(card, "scale", Vector2(1.0, 1.0), 0.3)
	tween.tween_property(card, "position:y", card.position.y, 0.3)

	# Reset colors based on selection
	if data.number == selected_chapter:
		tween.tween_property(bg, "modulate", Color(1.3, 1.2, 0.8), 0.3)
		tween.tween_property(shadow, "modulate", Color(0.8, 0.6, 0.2, 0.5), 0.3)
		tween.tween_property(glow, "modulate", Color(1, 1, 0.5, 0.3), 0.3)
	else:
		tween.tween_property(bg, "modulate", Color.WHITE, 0.3)
		tween.tween_property(shadow, "modulate", Color(0, 0, 0, 0.3), 0.3)
		tween.tween_property(glow, "modulate", Color(1, 1, 1, 0.1), 0.3)

# Lock animation pre nedostupné kapitoly
func show_locked_animation(card: Control):
	var shake_tween = create_tween()
	var original_pos = card.position
	for i in range(3):
		shake_tween.tween_property(card, "position:x", original_pos.x + 5, 0.05)
		shake_tween.tween_property(card, "position:x", original_pos.x - 5, 0.05)
	shake_tween.tween_property(card, "position:x", original_pos.x, 0.05)

func get_chapter_preview_path(chapter_number: int) -> String:
	# Použiť prvý obrázok z každej kapitoly ako preview
	match chapter_number:
		1:
			return "res://assets/Obrázky/Kapitola_1/1.png"
		2:
			return "res://assets/pozadia/Kapitola_2/1.png"
		3:
			return "res://assets/pozadia/Kapitola_3/1.png"
		4:
			return "res://assets/pozadia/Kapitola_4/1.png"
		5:
			return "res://assets/pozadia/Kapitola_5/1.png"
		6:
			return "res://assets/pozadia/Kapitola_6/1.png"
		_:
			return ""

func setup_button_style(button: Button):
	# Apply RAMCEK frame to button
	var ramcek_texture = load("res://assets/RAMCEK.png")

	# Create StyleBoxTexture for normal state
	var style_normal = StyleBoxTexture.new()
	style_normal.texture = ramcek_texture
	style_normal.texture_margin_left = 16
	style_normal.texture_margin_top = 16
	style_normal.texture_margin_right = 16
	style_normal.texture_margin_bottom = 16

	# Create StyleBoxTexture for hover state
	var style_hover = StyleBoxTexture.new()
	style_hover.texture = ramcek_texture
	style_hover.texture_margin_left = 16
	style_hover.texture_margin_top = 16
	style_hover.texture_margin_right = 16
	style_hover.texture_margin_bottom = 16
	style_hover.modulate_color = Color(1.2, 1.2, 1.0)  # Golden tint

	# Apply styles
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)
	button.add_theme_stylebox_override("pressed", style_hover)

	# Center text
	button.alignment = HORIZONTAL_ALIGNMENT_CENTER

func create_page_indicator():
	# Create page indicator below carousel
	var indicator_container = HBoxContainer.new()
	indicator_container.alignment = BoxContainer.ALIGNMENT_CENTER
	indicator_container.set_anchors_and_offsets_preset(Control.PRESET_CENTER_BOTTOM)
	indicator_container.offset_top = -100

	for i in range(total_pages):
		var dot = Label.new()
		if i == current_page:
			dot.text = "●"
			dot.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
		else:
			dot.text = "○"
			dot.add_theme_color_override("font_color", Color(0.5, 0.5, 0.5))

		dot.add_theme_font_size_override("font_size", 20)
		dot.custom_minimum_size = Vector2(30, 30)
		dot.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		indicator_container.add_child(dot)

	# Store reference for updates
	get_node("MainContainer/CarouselContainer").add_child(indicator_container)
	get_node("MainContainer/CarouselContainer").set_meta("page_indicator", indicator_container)

func update_page_indicator():
	var indicator = get_node("MainContainer/CarouselContainer").get_meta("page_indicator", null)
	if indicator:
		for i in range(indicator.get_child_count()):
			var dot = indicator.get_child(i)
			if i == current_page:
				dot.text = "●"
				dot.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
			else:
				dot.text = "○"
				dot.add_theme_color_override("font_color", Color(0.5, 0.5, 0.5))

func create_progress_indicator():
	# Create overall progress indicator at top
	var progress_container = VBoxContainer.new()
	progress_container.set_anchors_and_offsets_preset(Control.PRESET_TOP_WIDE)
	progress_container.offset_top = 80
	progress_container.offset_bottom = 120

	# Progress label
	var progress_label = Label.new()
	var completed_count = 0
	for data in chapter_data:
		if data.completed:
			completed_count += 1

	progress_label.text = "Pokrok: " + str(completed_count) + "/" + str(total_chapters) + " kapitol dokončených"
	progress_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	progress_label.add_theme_font_size_override("font_size", 16)
	progress_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6))
	progress_label.add_theme_constant_override("outline_size", 1)
	progress_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.6))

	# Progress bar background
	var progress_bg = NinePatchRect.new()
	progress_bg.texture = load("res://assets/RAMCEK.png")
	progress_bg.patch_margin_left = 8
	progress_bg.patch_margin_top = 8
	progress_bg.patch_margin_right = 8
	progress_bg.patch_margin_bottom = 8
	progress_bg.custom_minimum_size = Vector2(400, 20)
	progress_bg.modulate = Color(0.3, 0.3, 0.3, 0.8)

	# Progress bar fill
	var progress_fill = ColorRect.new()
	var progress_percent = float(completed_count) / float(total_chapters)
	progress_fill.color = Color(0.831, 0.686, 0.216, 0.8)
	progress_fill.set_anchors_and_offsets_preset(Control.PRESET_LEFT_WIDE)
	progress_fill.offset_left = 8
	progress_fill.offset_top = 8
	progress_fill.offset_bottom = -8
	progress_fill.offset_right = 8 + (384 * progress_percent)  # 384 = 400 - 16 (margins)

	progress_bg.add_child(progress_fill)
	progress_container.add_child(progress_label)
	progress_container.add_child(progress_bg)

	get_node("MainContainer").add_child(progress_container)
	get_node("MainContainer").move_child(progress_container, 1)  # After title
