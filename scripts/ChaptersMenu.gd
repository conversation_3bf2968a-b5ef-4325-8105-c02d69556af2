extends Control

# UI References
@onready var left_arrow = $MainContainer/CarouselContainer/LeftArrow
@onready var right_arrow = $MainContainer/CarouselContainer/RightArrow
@onready var chapter_grid = $MainContainer/CarouselContainer/ChapterGrid
@onready var play_button = $MainContainer/PlayButton
@onready var back_button = $MainContainer/BackButton
@onready var title_label = $MainContainer/TitleLabel

# Carousel settings
var current_page = 0
var chapters_per_page = 3
var total_chapters = 6
var total_pages = 2
var selected_chapter = 1

# Chapter data
var chapter_data = [
	{"number": 1, "title": "BÚRLIVÁ CESTA", "unlocked": true, "completed": false},
	{"number": 2, "title": "BRÁNA ZÁMKU", "unlocked": false, "completed": false},
	{"number": 3, "title": "PÁTRANIE V ZÁMKU", "unlocked": false, "completed": false},
	{"number": 4, "title": "TAJNÉ KRÍDLO", "unlocked": false, "completed": false},
	{"number": 5, "title": "TEMNÉ KRYPTY", "unlocked": false, "completed": false},
	{"number": 6, "title": "FINÁLNA BITKA", "unlocked": false, "completed": false}
]

func _ready():
	setup_ui()
	load_chapter_progress()
	setup_chapter_cards()
	update_carousel_display()
	connect_signals()

func setup_ui():
	# Setup title
	title_label.text = "KAPITOLY"

	# Setup buttons
	play_button.text = "HRAŤ KAPITOLU"
	back_button.text = "SPÄŤ"

	# Apply RAMCEK styling to buttons
	setup_button_style(play_button)
	setup_button_style(back_button)
	setup_button_style(left_arrow)
	setup_button_style(right_arrow)

	# Setup arrows
	left_arrow.add_theme_font_size_override("font_size", 24)
	right_arrow.add_theme_font_size_override("font_size", 24)

	# Setup grid
	chapter_grid.columns = chapters_per_page
	chapter_grid.add_theme_constant_override("h_separation", 20)

func load_chapter_progress():
	# Load from GameManager
	for i in range(total_chapters):
		var chapter_num = i + 1
		chapter_data[i].unlocked = GameManager.is_chapter_unlocked(chapter_num)
		chapter_data[i].completed = chapter_num in GameManager.completed_chapters

func setup_chapter_cards():
	# Create chapter cards
	for i in range(total_chapters):
		var card = create_chapter_card(chapter_data[i])
		chapter_grid.add_child(card)

func create_chapter_card(data: Dictionary) -> Control:
	var card = Control.new()
	card.custom_minimum_size = Vector2(220, 280)

	# Background frame
	var bg = NinePatchRect.new()
	bg.texture = load("res://assets/RAMCEK.png")
	bg.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	bg.patch_margin_left = 16
	bg.patch_margin_top = 16
	bg.patch_margin_right = 16
	bg.patch_margin_bottom = 16
	card.add_child(bg)

	# Container for content
	var container = VBoxContainer.new()
	container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	container.add_theme_constant_override("margin_left", 15)
	container.add_theme_constant_override("margin_top", 15)
	container.add_theme_constant_override("margin_right", 15)
	container.add_theme_constant_override("margin_bottom", 15)
	card.add_child(container)

	# Chapter number
	var number_label = Label.new()
	number_label.text = "KAPITOLA " + str(data.number)
	number_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	number_label.add_theme_font_size_override("font_size", 16)
	number_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	container.add_child(number_label)

	# Chapter title
	var title_label = Label.new()
	title_label.text = data.title
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	title_label.add_theme_font_size_override("font_size", 14)
	title_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6))
	container.add_child(title_label)

	# Spacer
	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 10)
	container.add_child(spacer)

	# Chapter preview image
	var preview_path = get_chapter_preview_path(data.number)
	if ResourceLoader.exists(preview_path):
		var preview = TextureRect.new()
		preview.texture = load(preview_path)
		preview.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		preview.custom_minimum_size = Vector2(180, 120)
		container.add_child(preview)
	else:
		# Fallback to colored rectangle
		var fallback = ColorRect.new()
		fallback.color = Color(0.2, 0.2, 0.3, 0.8)
		fallback.custom_minimum_size = Vector2(180, 120)
		container.add_child(fallback)

	# Progress/Lock icon
	var icon_container = HBoxContainer.new()
	icon_container.alignment = BoxContainer.ALIGNMENT_CENTER
	var icon_label = Label.new()
	if data.completed:
		icon_label.text = "✓ DOKONČENÉ"
		icon_label.add_theme_color_override("font_color", Color.GREEN)
	elif data.unlocked:
		icon_label.text = "DOSTUPNÉ"
		icon_label.add_theme_color_override("font_color", Color.YELLOW)
	else:
		icon_label.text = "🔒 UZAMKNUTÉ"
		icon_label.add_theme_color_override("font_color", Color.GRAY)

	icon_label.add_theme_font_size_override("font_size", 12)
	icon_container.add_child(icon_label)
	container.add_child(icon_container)

	# Make card clickable
	var button = Button.new()
	button.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	button.flat = true
	button.pressed.connect(func(): select_chapter(data.number))

	# Add hover effects
	button.mouse_entered.connect(func(): _on_card_mouse_entered(card))
	button.mouse_exited.connect(func(): _on_card_mouse_exited(card))

	card.add_child(button)

	# Visual feedback for selection
	if data.number == selected_chapter:
		bg.modulate = Color(1.2, 1.2, 1.0)  # Golden highlight
	else:
		bg.modulate = Color.WHITE

	# Store references for animations
	card.set_meta("bg", bg)
	card.set_meta("data", data)

	return card

func update_carousel_display():
	# Hide all cards first
	for card in chapter_grid.get_children():
		card.visible = false

	# Show cards for current page
	var start_index = current_page * chapters_per_page
	var end_index = min(start_index + chapters_per_page, total_chapters)

	for i in range(start_index, end_index):
		if i < chapter_grid.get_child_count():
			chapter_grid.get_child(i).visible = true

	# Update arrow visibility
	left_arrow.disabled = (current_page == 0)
	right_arrow.disabled = (current_page >= total_pages - 1)

	# Update arrow visual state
	if left_arrow.disabled:
		left_arrow.modulate = Color(0.5, 0.5, 0.5)
	else:
		left_arrow.modulate = Color.WHITE

	if right_arrow.disabled:
		right_arrow.modulate = Color(0.5, 0.5, 0.5)
	else:
		right_arrow.modulate = Color.WHITE

func connect_signals():
	left_arrow.pressed.connect(_on_left_arrow_pressed)
	right_arrow.pressed.connect(_on_right_arrow_pressed)
	play_button.pressed.connect(_on_play_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)

func _on_left_arrow_pressed():
	if current_page > 0:
		current_page -= 1
		AudioManager.play_menu_button_sound()
		animate_carousel_transition("left")

func _on_right_arrow_pressed():
	if current_page < total_pages - 1:
		current_page += 1
		AudioManager.play_menu_button_sound()
		animate_carousel_transition("right")

func animate_carousel_transition(direction: String):
	var tween = create_tween()

	# Slide out current page
	if direction == "left":
		tween.tween_property(chapter_grid, "position:x", 100, 0.3)
	else:
		tween.tween_property(chapter_grid, "position:x", -100, 0.3)

	# Update display
	tween.tween_callback(update_carousel_display)

	# Slide in new page
	if direction == "left":
		chapter_grid.position.x = -100
	else:
		chapter_grid.position.x = 100

	tween.tween_property(chapter_grid, "position:x", 0, 0.3)

func select_chapter(chapter_number: int):
	if not chapter_data[chapter_number - 1].unlocked:
		AudioManager.play_puzzle_error_sound()
		# Find the card and show lock animation
		var card_index = chapter_number - 1
		if card_index < chapter_grid.get_child_count():
			var card = chapter_grid.get_child(card_index)
			show_locked_animation(card)
		return

	selected_chapter = chapter_number
	AudioManager.play_menu_button_sound()

	# Update visual selection
	refresh_chapter_cards()

func refresh_chapter_cards():
	# Remove old cards and recreate
	for card in chapter_grid.get_children():
		card.queue_free()

	await get_tree().process_frame
	setup_chapter_cards()
	update_carousel_display()

func _on_play_button_pressed():
	if not chapter_data[selected_chapter - 1].unlocked:
		AudioManager.play_puzzle_error_sound()
		return

	AudioManager.play_menu_button_sound()
	GameManager.go_to_chapter(selected_chapter)

func _on_back_button_pressed():
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_button_pressed()

# Hover effects pre cards
func _on_card_mouse_entered(card: Control):
	var data = card.get_meta("data")
	if data.unlocked:
		var tween = create_tween()
		tween.tween_property(card, "scale", Vector2(1.05, 1.05), 0.2)
		AudioManager.play_ui_sound("res://audio/UI_SOUNDS/menu_sound.wav")

func _on_card_mouse_exited(card: Control):
	var tween = create_tween()
	tween.tween_property(card, "scale", Vector2(1.0, 1.0), 0.2)

# Lock animation pre nedostupné kapitoly
func show_locked_animation(card: Control):
	var shake_tween = create_tween()
	var original_pos = card.position
	for i in range(3):
		shake_tween.tween_property(card, "position:x", original_pos.x + 5, 0.05)
		shake_tween.tween_property(card, "position:x", original_pos.x - 5, 0.05)
	shake_tween.tween_property(card, "position:x", original_pos.x, 0.05)

func get_chapter_preview_path(chapter_number: int) -> String:
	# Použiť prvý obrázok z každej kapitoly ako preview
	match chapter_number:
		1:
			return "res://assets/Obrázky/Kapitola_1/1.png"
		2:
			return "res://assets/pozadia/Kapitola_2/1.png"
		3:
			return "res://assets/pozadia/Kapitola_3/1.png"
		4:
			return "res://assets/pozadia/Kapitola_4/1.png"
		5:
			return "res://assets/pozadia/Kapitola_5/1.png"
		6:
			return "res://assets/pozadia/Kapitola_6/1.png"
		_:
			return ""

func setup_button_style(button: Button):
	# Apply RAMCEK frame to button
	var ramcek_texture = load("res://assets/RAMCEK.png")

	# Create StyleBoxTexture for normal state
	var style_normal = StyleBoxTexture.new()
	style_normal.texture = ramcek_texture
	style_normal.texture_margin_left = 16
	style_normal.texture_margin_top = 16
	style_normal.texture_margin_right = 16
	style_normal.texture_margin_bottom = 16

	# Create StyleBoxTexture for hover state
	var style_hover = StyleBoxTexture.new()
	style_hover.texture = ramcek_texture
	style_hover.texture_margin_left = 16
	style_hover.texture_margin_top = 16
	style_hover.texture_margin_right = 16
	style_hover.texture_margin_bottom = 16
	style_hover.modulate_color = Color(1.2, 1.2, 1.0)  # Golden tint

	# Apply styles
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)
	button.add_theme_stylebox_override("pressed", style_hover)

	# Center text
	button.alignment = HORIZONTAL_ALIGNMENT_CENTER
