[gd_scene load_steps=6 format=3 uid="uid://cyavoaqkqxqxq"]

[ext_resource type="Script" path="res://scripts/ChaptersMenu.gd" id="1_chapters"]
[ext_resource type="Texture2D" path="res://assets/MENU.png" id="2_bg"]
[ext_resource type="Theme" path="res://themes/GothicTheme.tres" id="3_theme"]
[ext_resource type="Texture2D" path="res://assets/RAMCEK.png" id="4_ramcek"]

[sub_resource type="LabelSettings" id="LabelSettings_title"]
font_size = 32
font_color = Color(0.831, 0.686, 0.216, 1)
outline_size = 3
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 2
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(3, 3)

[node name="ChaptersMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("3_theme")
script = ExtResource("1_chapters")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
stretch_mode = 1

[node name="MainContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 40.0
offset_top = 40.0
offset_right = -40.0
offset_bottom = -40.0

[node name="TitleLabel" type="Label" parent="MainContainer"]
layout_mode = 2
text = "KAPITOLY"
label_settings = SubResource("LabelSettings_title")
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="CarouselContainer" type="Control" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="LeftArrow" type="Button" parent="MainContainer/CarouselContainer"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 20.0
offset_top = -30.0
offset_right = 80.0
offset_bottom = 30.0
text = "◄"

[node name="ChapterGrid" type="GridContainer" parent="MainContainer/CarouselContainer"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -360.0
offset_top = -150.0
offset_right = 360.0
offset_bottom = 150.0
columns = 3

[node name="RightArrow" type="Button" parent="MainContainer/CarouselContainer"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -80.0
offset_top = -30.0
offset_right = -20.0
offset_bottom = 30.0
text = "►"

[node name="Spacer2" type="Control" parent="MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="PlayButton" type="Button" parent="MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
text = "HRAŤ KAPITOLU"

[node name="Spacer3" type="Control" parent="MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="BackButton" type="Button" parent="MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "SPÄŤ"
